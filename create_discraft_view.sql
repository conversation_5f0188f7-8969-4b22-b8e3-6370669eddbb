-- Create or replace the v_stats_by_osl_discraft view
-- Updated to set order quantity to 0 when is_currently_available is FALSE

CREATE OR REPLACE VIEW public.v_stats_by_osl_discraft AS
SELECT
  i.id,
  i.mold_name,
  i.plastic_name,
  i.min_weight,
  i.max_weight,
  i.color_name,
  i.stamp_name,
  i.vendor_product_code,
  i.is_orderable,
  i.is_currently_available,
  i.cost_price,
  i.excel_row,
  i.excel_column,
  i.import_batch_id,
  i.created_at,
  i.vendor_description,
  i.raw_line_type,
  i.raw_model,
  i.excel_mapping_key,
  i.excel_row_hint,
  i.raw_weight_range,
  i.import_file_hash,
  i.raw_status,
  i.calculated_mps_id,
  COALESCE(in_stock.count, 0::bigint) AS in_stock_count,
  COALESCE(recent_sales.count, 0::bigint) AS sold_last_90_days,
  CASE 
    WHEN i.is_currently_available = false THEN 0::bigint
    ELSE GREATEST(
      COALESCE(recent_sales.count, 0::bigint) - COALESCE(in_stock.count, 0::bigint),
      0::bigint
    )
  END AS "order"
FROM
  it_discraft_order_sheet_lines i
  LEFT JOIN LATERAL (
    SELECT
      count(*) AS count
    FROM
      t_discs d
      JOIN t_order_sheet_lines osl ON d.order_sheet_line_id = osl.id
    WHERE
      osl.mps_id = i.calculated_mps_id
      AND d.weight >= COALESCE(i.min_weight::real, d.weight)
      AND d.weight <= COALESCE(i.max_weight::real, d.weight)
      AND d.sold_date IS NULL
  ) in_stock ON true
  LEFT JOIN LATERAL (
    SELECT
      count(*) AS count
    FROM
      t_discs d
      JOIN t_order_sheet_lines osl ON d.order_sheet_line_id = osl.id
    WHERE
      osl.mps_id = i.calculated_mps_id
      AND d.weight >= COALESCE(i.min_weight::real, d.weight)
      AND d.weight <= COALESCE(i.max_weight::real, d.weight)
      AND d.sold_date >= (CURRENT_DATE - '90 days'::interval)
  ) recent_sales ON true;
