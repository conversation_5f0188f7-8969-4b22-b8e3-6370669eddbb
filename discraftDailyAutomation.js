import { createClient } from '@supabase/supabase-js';
import nodemailer from 'nodemailer';
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize Supabase client
const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzc4NjU0MSwiZXhwIjoyMDQ5MzYyNTQxfQ.FQyPTgdBP83VhuykdlZkagHADc5nBmx7-yd0JYt5aP8';
const supabase = createClient(supabaseUrl, supabaseKey);

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

// Email configuration
const emailConfig = {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
        user: process.env.EMAIL_USER || '<EMAIL>',
        pass: process.env.EMAIL_PASS || 'your-app-password'
    }
};

async function downloadDiscraftFile() {
    console.log('📥 Downloading Discraft vendor file...');
    
    try {
        const vendorUrl = 'https://www.discgolf.discraft.com/forms/stock.xlsx';
        const outputPath = path.join(__dirname, 'data', 'external data', 'discraftstock.xlsx');
        
        // Download the file
        const response = await fetch(vendorUrl);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const buffer = await response.buffer();
        
        // Ensure directory exists
        const dir = path.dirname(outputPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        
        // Write the file
        fs.writeFileSync(outputPath, buffer);
        
        console.log(`✅ Downloaded ${buffer.length} bytes to ${outputPath}`);
        return { success: true, filePath: outputPath, fileSize: buffer.length };
        
    } catch (error) {
        console.error('❌ Download failed:', error);
        return { success: false, error: error.message };
    }
}

async function runDiscraftImport() {
    console.log('🔄 Running Discraft import...');

    try {
        // Make API call to run import
        const response = await fetch('http://localhost:3001/api/discraft/import', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        });

        const result = await response.json();
        console.log('✅ Import completed:', result);
        return result;

    } catch (error) {
        console.error('❌ Import failed:', error);
        return { success: false, error: error.message };
    }
}

async function calculateMpsMatching() {
    console.log('🎯 Calculating MPS matching...');
    
    try {
        // Make API call to calculate matching
        const response = await fetch('http://localhost:3001/api/discraft/calculate-mps', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
        
        const result = await response.json();
        console.log('✅ MPS matching completed:', result);
        return result;
        
    } catch (error) {
        console.error('❌ MPS matching failed:', error);
        return { success: false, error: error.message };
    }
}

async function getOrderSummary() {
    console.log('📊 Calculating order summary...');
    
    try {
        // Query the view to get order totals
        const { data, error } = await supabase
            .from('v_stats_by_osl_discraft')
            .select('"order", mold_name, plastic_name, is_currently_available')
            .gt('order', 0);
        
        if (error) {
            throw new Error(error.message);
        }
        
        const totalOrderQuantity = data.reduce((sum, item) => sum + (item.order || 0), 0);
        const uniqueMolds = [...new Set(data.map(item => item.mold_name))].length;
        const uniquePlastics = [...new Set(data.map(item => item.plastic_name))].length;
        
        // Group by mold for summary
        const moldSummary = data.reduce((acc, item) => {
            if (!acc[item.mold_name]) {
                acc[item.mold_name] = 0;
            }
            acc[item.mold_name] += item.order;
            return acc;
        }, {});
        
        // Top 10 molds by order quantity
        const topMolds = Object.entries(moldSummary)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10);
        
        console.log(`✅ Order summary: ${totalOrderQuantity} total discs, ${uniqueMolds} molds, ${uniquePlastics} plastics`);
        
        return {
            success: true,
            totalOrderQuantity,
            uniqueMolds,
            uniquePlastics,
            topMolds,
            detailedData: data
        };
        
    } catch (error) {
        console.error('❌ Order summary failed:', error);
        return { success: false, error: error.message };
    }
}

async function sendEmailReport(downloadResult, importResult, matchingResult, orderSummary) {
    console.log('📧 Sending email report...');

    try {
        // Check if email is configured
        if (!process.env.EMAIL_USER || process.env.EMAIL_USER === '<EMAIL>') {
            console.log('📧 Email not configured, printing report to console instead...');
            printReportToConsole(downloadResult, importResult, matchingResult, orderSummary);
            return { success: true, message: 'Report printed to console (email not configured)' };
        }

        const transporter = nodemailer.createTransport(emailConfig);
        
        const currentDate = new Date().toLocaleDateString('en-US', {
            timeZone: 'America/Chicago',
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        
        const currentTime = new Date().toLocaleTimeString('en-US', {
            timeZone: 'America/Chicago',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        let emailBody = `
<h2>📊 Daily Discraft Order Report - ${currentDate}</h2>
<p><strong>Generated:</strong> ${currentTime} CST</p>

<h3>🎯 Order Summary</h3>
`;
        
        if (orderSummary.success) {
            emailBody += `
<div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;">
    <h4 style="color: #2d5a2d; margin: 0 0 10px 0;">📦 Total Order Quantity: ${orderSummary.totalOrderQuantity} discs</h4>
    <p><strong>Unique Molds:</strong> ${orderSummary.uniqueMolds} | <strong>Unique Plastics:</strong> ${orderSummary.uniquePlastics}</p>
</div>

<h4>🏆 Top 10 Molds to Order:</h4>
<table style="border-collapse: collapse; width: 100%; margin: 10px 0;">
    <tr style="background: #f0f0f0;">
        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Mold</th>
        <th style="border: 1px solid #ddd; padding: 8px; text-align: right;">Quantity</th>
    </tr>
`;
            
            orderSummary.topMolds.forEach(([mold, quantity]) => {
                emailBody += `
    <tr>
        <td style="border: 1px solid #ddd; padding: 8px;">${mold}</td>
        <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">${quantity}</td>
    </tr>`;
            });
            
            emailBody += `</table>`;
        } else {
            emailBody += `<p style="color: red;">❌ Order calculation failed: ${orderSummary.error}</p>`;
        }
        
        emailBody += `
<h3>🔄 Process Status</h3>
<ul>
    <li><strong>Download:</strong> ${downloadResult.success ? '✅ Success' : '❌ Failed - ' + downloadResult.error}</li>
    <li><strong>Import:</strong> ${importResult.success ? '✅ Success' : '❌ Failed - ' + importResult.error}</li>
    <li><strong>Matching:</strong> ${matchingResult.success ? '✅ Success' : '❌ Failed - ' + matchingResult.error}</li>
    <li><strong>Order Calculation:</strong> ${orderSummary.success ? '✅ Success' : '❌ Failed - ' + orderSummary.error}</li>
</ul>

<p><small>This report was automatically generated by the Discraft Daily Automation system.</small></p>
`;
        
        const mailOptions = {
            from: emailConfig.auth.user,
            to: '<EMAIL>',
            subject: `📊 Daily Discraft Order Report - ${orderSummary.success ? orderSummary.totalOrderQuantity + ' discs' : 'Error'}`,
            html: emailBody
        };
        
        await transporter.sendMail(mailOptions);
        console.log('✅ Email sent successfully');
        return { success: true };
        
    } catch (error) {
        console.error('❌ Email failed:', error);
        return { success: false, error: error.message };
    }
}

function printReportToConsole(downloadResult, importResult, matchingResult, orderSummary) {
    const currentDate = new Date().toLocaleDateString('en-US', {
        timeZone: 'America/Chicago',
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    const currentTime = new Date().toLocaleTimeString('en-US', {
        timeZone: 'America/Chicago',
        hour: '2-digit',
        minute: '2-digit'
    });

    console.log('\n' + '='.repeat(60));
    console.log(`📊 DAILY DISCRAFT ORDER REPORT - ${currentDate}`);
    console.log(`Generated: ${currentTime} CST`);
    console.log('='.repeat(60));

    if (orderSummary.success) {
        console.log(`\n🎯 ORDER SUMMARY`);
        console.log(`📦 Total Order Quantity: ${orderSummary.totalOrderQuantity} discs`);
        console.log(`🥏 Unique Molds: ${orderSummary.uniqueMolds} | 🎨 Unique Plastics: ${orderSummary.uniquePlastics}`);

        console.log(`\n🏆 TOP 10 MOLDS TO ORDER:`);
        orderSummary.topMolds.forEach(([mold, quantity], index) => {
            console.log(`   ${index + 1}. ${mold}: ${quantity} discs`);
        });
    } else {
        console.log(`\n❌ Order calculation failed: ${orderSummary.error}`);
    }

    console.log(`\n🔄 PROCESS STATUS:`);
    console.log(`   Download: ${downloadResult.success ? '✅ Success' : '❌ Failed - ' + downloadResult.error}`);
    console.log(`   Import: ${importResult.success ? '✅ Success' : '❌ Failed - ' + importResult.error}`);
    console.log(`   Matching: ${matchingResult.success ? '✅ Success' : '❌ Failed - ' + matchingResult.error}`);
    console.log(`   Order Calculation: ${orderSummary.success ? '✅ Success' : '❌ Failed - ' + orderSummary.error}`);

    console.log('\n📧 Email not configured - configure EMAIL_USER and EMAIL_PASS in .env to receive email reports');
    console.log('='.repeat(60) + '\n');
}

async function runDailyAutomation() {
    console.log('🚀 Starting Discraft Daily Automation...');
    console.log(`📅 ${new Date().toLocaleString('en-US', { timeZone: 'America/Chicago' })} CST\n`);
    
    // Step 1: Download vendor file
    const downloadResult = await downloadDiscraftFile();
    
    // Step 2: Run import (only if download succeeded)
    let importResult = { success: false, error: 'Download failed' };
    if (downloadResult.success) {
        importResult = await runDiscraftImport();
    }
    
    // Step 3: Calculate MPS matching (only if import succeeded)
    let matchingResult = { success: false, error: 'Import failed' };
    if (importResult.success) {
        matchingResult = await calculateMpsMatching();
    }
    
    // Step 4: Get order summary (always try, even if previous steps failed)
    const orderSummary = await getOrderSummary();
    
    // Step 5: Send email report
    const emailResult = await sendEmailReport(downloadResult, importResult, matchingResult, orderSummary);
    
    console.log('\n📊 Daily Automation Summary:');
    console.log(`   Download: ${downloadResult.success ? '✅' : '❌'}`);
    console.log(`   Import: ${importResult.success ? '✅' : '❌'}`);
    console.log(`   Matching: ${matchingResult.success ? '✅' : '❌'}`);
    console.log(`   Order Summary: ${orderSummary.success ? '✅' : '❌'}`);
    console.log(`   Email: ${emailResult.success ? '✅' : '❌'}`);
    
    if (orderSummary.success) {
        console.log(`\n🎯 Total Order Quantity: ${orderSummary.totalOrderQuantity} discs`);
    }
    
    console.log('\n🏁 Daily automation completed!');
}

// Export for use in scheduler
export default runDailyAutomation;

// Run immediately if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runDailyAutomation().catch(console.error);
}
