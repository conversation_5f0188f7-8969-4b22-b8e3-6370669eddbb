import { createClient } from '@supabase/supabase-js';
import <PERSON> from 'papaparse';
import fs from 'fs/promises';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: SUPABASE_URL or SUPABASE_KEY environment variables are not set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function importVeeqoSellables() {
  console.log('⏳ Starting import of Veeqo sellables data...');

  // First, check if the table exists and get its structure
  console.log('🔍 Checking table structure...');
  try {
    const { data: tableCheck, error: tableError } = await supabase
      .from('imported_table_veeqo_sellables_export')
      .select('*')
      .limit(1);

    if (tableError) {
      console.error('❌ Error accessing table:', tableError);
      console.log('🔧 The table might not exist or have permission issues.');

      // Try to get table info from information_schema
      const { data: columns, error: columnsError } = await supabase
        .rpc('exec_sql', {
          sql_query: `
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'imported_table_veeqo_sellables_export'
            AND table_schema = 'public'
            ORDER BY ordinal_position;
          `
        });

      if (columnsError) {
        console.error('❌ Could not get table schema:', columnsError);
      } else if (columns && columns.length > 0) {
        console.log('📋 Table schema:');
        columns.forEach(col => {
          console.log(`   ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
        });
      }
    } else {
      console.log('✅ Table exists and is accessible');
      if (tableCheck && tableCheck.length > 0) {
        console.log('📋 Table columns from existing data:');
        Object.keys(tableCheck[0]).forEach(key => {
          console.log(`   - ${key}`);
        });
      }
    }
  } catch (error) {
    console.error('❌ Error checking table:', error);
  }

  // Define the path to your CSV file
  const filePath = path.resolve('C:\\Users\\<USER>\\supabase_project\\data\\external data\\veeqo_sellables_export.csv');

  try {
    // Read the CSV file
    console.log(`📂 Reading CSV file from: ${filePath}`);
    const fileContent = await fs.readFile(filePath, 'utf-8');

    // Parse the CSV data
    console.log('🔄 Parsing CSV data...');
    const { data: parsedData, errors } = Papa.parse(fileContent, {
      header: true,
      skipEmptyLines: true,
      dynamicTyping: true,
      delimiter: ',',
    });

    if (errors.length > 0) {
      console.warn('⚠️ CSV Parsing Warnings:', errors.slice(0, 5)); // Show first 5 errors
    }

    console.log(`✅ CSV parsed successfully. Found ${parsedData.length} records.`);

    // Show CSV headers
    if (parsedData.length > 0) {
      console.log('📋 CSV Headers:');
      Object.keys(parsedData[0]).forEach(key => {
        console.log(`   - ${key}`);
      });

      // Show a sample record
      console.log('📄 Sample record:');
      console.log(JSON.stringify(parsedData[0], null, 2));
    }

    // Skip deleting existing data - the table is already truncated by adminServer.js
    console.log('ℹ️ Skipping deletion of existing data - table should already be truncated.');

    // Insert data in chunks to avoid timeouts
    const chunkSize = 1000;
    console.log(`📤 Inserting data in chunks of ${chunkSize}...`);

    for (let i = 0; i < parsedData.length; i += chunkSize) {
      const chunk = parsedData.slice(i, i + chunkSize);
      console.log(`⏳ Inserting chunk ${Math.floor(i/chunkSize) + 1} of ${Math.ceil(parsedData.length/chunkSize)} (${chunk.length} records)...`);

      const { data: insertData, error: insertError } = await supabase
        .from('imported_table_veeqo_sellables_export')
        .upsert(chunk, { onConflict: 'product_id' });

      if (insertError) {
        console.error(`❌ Error inserting chunk ${Math.floor(i/chunkSize) + 1}:`, insertError);
        console.error(`❌ Error details:`, JSON.stringify(insertError, null, 2));

        // Log a sample of the data that failed to insert
        console.error(`❌ Sample data from failed chunk:`, JSON.stringify(chunk.slice(0, 2), null, 2));

        // Don't exit on error, continue with next chunk
        // process.exit(1);
      } else {
        console.log(`✅ Chunk ${Math.floor(i/chunkSize) + 1} inserted successfully.`);
        if (insertData) {
          console.log(`   📊 Inserted ${insertData.length} records`);
        }
      }
    }

    console.log('✅ Import completed successfully!');

    // Verify the import by checking record count
    console.log('🔍 Verifying import...');
    try {
      const { count, error: countError } = await supabase
        .from('imported_table_veeqo_sellables_export')
        .select('*', { count: 'exact', head: true });

      if (countError) {
        console.error('❌ Error counting records:', countError);
      } else {
        console.log(`📊 Total records in table after import: ${count}`);
        if (count === 0) {
          console.warn('⚠️ WARNING: No records found in table after import!');
        } else if (count !== parsedData.length) {
          console.warn(`⚠️ WARNING: Record count mismatch! Expected: ${parsedData.length}, Found: ${count}`);
        } else {
          console.log('✅ Record count matches expected!');
        }
      }
    } catch (verifyError) {
      console.error('❌ Error verifying import:', verifyError);
    }

  } catch (error) {
    console.error('❌ Error during import process:', error);
    process.exit(1);
  }
}

// Run the import function
importVeeqoSellables();