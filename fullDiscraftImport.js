import XLSX from 'xlsx';
import { createClient } from '@supabase/supabase-js';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import fs from 'fs';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase environment variables');
    console.error('Please ensure SUPABASE_URL and SUPABASE_KEY are set in your .env file');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Robust mapping utilities
function createProductKey(rawLine, rawModel, weightRange) {
    const line = rawLine || 'NO_LINE';
    const model = rawModel || 'NO_MODEL';
    return `${line}|${model}|${weightRange}`;
}

function calculateFileHash(filePath) {
    const stats = fs.statSync(filePath);
    return `${stats.size}_${stats.mtime.getTime()}`;
}

// Function to parse weight range from header text
function parseWeightHeader(headerText) {
    if (!headerText) return null;

    const text = headerText.toString().trim();

    // Handle single weight like "150" or "150g" - should be parsed as "150-159"
    const singleMatch = text.match(/^(\d+)g?$/);
    if (singleMatch) {
        const weight = parseInt(singleMatch[1]);
        const maxWeight = weight + 9; // 150 becomes 150-159
        return { min: weight, max: maxWeight, name: `${weight}-${maxWeight}g` };
    }

    // Handle range like "141-150", "151-159", "160-166"
    const rangeMatch = text.match(/^(\d+)-(\d+)g?$/);
    if (rangeMatch) {
        const min = parseInt(rangeMatch[1]);
        const max = parseInt(rangeMatch[2]);
        return { min: min, max: max, name: `${min}-${max}g` };
    }

    // Handle "177+" format
    const plusMatch = text.match(/^(\d+)\+$/);
    if (plusMatch) {
        const min = parseInt(plusMatch[1]);
        return { min: min, max: 180, name: `${min}+g` }; // Assume max 180g
    }

    return null;
}

// Function to find weight headers in a row
function findWeightHeaders(row, startCol = 11, endCol = 17) {
    const weightHeaders = {};

    for (let col = startCol; col <= endCol; col++) {
        const headerText = row[col];
        const weightInfo = parseWeightHeader(headerText);

        if (weightInfo) {
            const columnLetter = String.fromCharCode(65 + col); // Convert to A, B, C, etc.
            weightHeaders[col] = {
                ...weightInfo,
                letter: columnLetter,
                columnIndex: col
            };
        }
    }

    return weightHeaders;
}

// Legacy function for backward compatibility - now uses dynamic headers
function getWeightRange(columnIndex, currentWeightHeaders = null) {
    // If we have current weight headers, use them
    if (currentWeightHeaders && currentWeightHeaders[columnIndex]) {
        return currentWeightHeaders[columnIndex];
    }

    // Fallback to old hardcoded mappings for sections without headers
    const weightMappings = {
        12: { min: 150, max: 150, name: '150g', letter: 'L' },
        13: { min: 160, max: 166, name: '160-166g', letter: 'M' },
        14: { min: 167, max: 169, name: '167-169g', letter: 'N' },
        15: { min: 170, max: 172, name: '170-172g', letter: 'O' },
        16: { min: 173, max: 174, name: '173-174g', letter: 'P' },
        17: { min: 175, max: 176, name: '175-176g', letter: 'Q' },
        18: { min: 177, max: 180, name: '177+', letter: 'R' }
    };

    return weightMappings[columnIndex] || null;
}

function getAssortedWeightRange() {
    // For "Assorted" weights, use 160-180g range
    return { min: 160, max: 180, name: 'Assorted', letter: 'ASSORTED' };
}

function extractSuperColorStamp(model) {
    if (!model) return 'SuperColor';

    const modelStr = model.toString().trim();

    // Extract stamp from SuperColor Gallery descriptions
    if (modelStr.includes('SuperColor Gallery Buzzz -')) {
        const stampPart = modelStr.replace(/.*SuperColor Gallery Buzzz\s*-\s*/, '').trim();

        // Handle special cases
        if (stampPart.includes('Bali')) return 'SuperColor Bali';
        if (stampPart.includes('Bunksy')) return 'SuperColor Bunksy';
        if (stampPart.includes('Demise')) return 'SuperColor Demise';
        if (stampPart.includes('Earth')) return 'SuperColor Earth';
        if (stampPart.includes('Fire')) return 'SuperColor Nebula Fire';
        if (stampPart.includes('Ancient Alien')) return 'Brian Allen Ancient Alien';
        if (stampPart.includes('Astronaut')) return 'SuperColor Astronaut';
        if (stampPart.includes('Owl')) return 'SuperColor Owl';
        if (stampPart.includes('Lichten')) return 'SuperColor Lichten';
        if (stampPart.includes('Moon')) return 'SuperColor Moon';

        // Default: use the last word as stamp
        const words = stampPart.split(/\s+/);
        const lastWord = words[words.length - 1];
        return `SuperColor ${lastWord}`;
    }

    // Handle FULL FOIL SuperColor
    if (modelStr.includes('FULL FOIL SuperColor')) {
        const stampPart = modelStr.replace(/.*FULL FOIL SuperColor\s+Buzzz\s*-\s*/, '').trim();
        return `FULL FOIL SuperColor ${stampPart}`;
    }

    return 'SuperColor';
}

function parseSpecialSections(rowData, row) {
    const col1 = rowData.col_1?.toString().trim() || '';
    const col4 = rowData.col_4?.toString().trim() || '';

    // Check all columns for skip patterns (some text might be in different columns)
    const allText = Object.values(rowData).join(' ').toLowerCase();

    // Skip McBeth Apparel section and everything below it
    if (col1.includes('McBeth Apparel') || col4.includes('McBeth Apparel')) {
        console.log(`Skipping McBeth Apparel section at row ${row + 1} - stopping import here`);
        return { skip: true, stopImport: true };
    }

    // Skip Total Golf Discs entries
    if (col1.includes('Total Golf Discs') || col4.includes('Total Golf Discs')) {
        console.log(`Skipping Total Golf Discs at row ${row + 1}: ${col1 || col4}`);
        return { skip: true };
    }

    // Skip DGA informational content
    if (allText.includes('dga and discraft news') ||
        allText.includes('dga and discraft have joined forces') ||
        allText.includes('discraft will now be distributing dga') ||
        allText.includes('dga golf discs') ||
        col1.includes('DGA') && (col1.includes('news') || col1.includes('joined') || col1.includes('distributing'))) {
        console.log(`Skipping DGA info section at row ${row + 1}: ${col1 || col4}`);
        return { skip: true };
    }

    // Skip plastic line descriptions
    if (allText.includes('spark line: a transparent') ||
        allText.includes('proline: a premium, opaque blend') ||
        allText.includes('d-line: a baseline, opaque blend') ||
        allText.includes('d-line stone: a stiffer') ||
        allText.includes('signature line: a unique rubber blend') ||
        allText.includes('new atmos line: dga') ||
        allText.includes('contrasting radiating colors')) {
        console.log(`Skipping plastic description at row ${row + 1}: ${col1 || col4}`);
        return { skip: true };
    }

    // Skip promotional headers
    if (allText.includes('new release') ||
        col1.toLowerCase() === 'new release' ||
        col4.toLowerCase() === 'new release') {
        console.log(`Skipping promotional header at row ${row + 1}: ${col1 || col4}`);
        return { skip: true };
    }

    // Skip summer cleaning/clearance sections
    if (col1.includes('Summer cleaning') ||
        col1.includes('SALE ITEMS') ||
        col1.includes('Random Golf discs') ||
        col4.includes('SALE ITEMS') ||
        col4.includes('Random Golf discs') ||
        col4.includes('Paul McBeth Proto Kratos') ||
        col4.includes('Missy Gannon Tour Series') ||
        allText.includes('discontinued/sale items') ||
        allText.includes('first come first serve')) {
        console.log(`Skipping clearance section at row ${row + 1}: ${col1 || col4}`);
        return { skip: true };
    }

    // Skip generic headers
    if (col1.toLowerCase() === 'golf discs' ||
        col4.toLowerCase() === 'golf discs' ||
        col1.toLowerCase() === 'order qty' ||
        col4.toLowerCase() === 'order qty') {
        console.log(`Skipping generic header at row ${row + 1}: ${col1 || col4}`);
        return { skip: true };
    }

    // Handle sections with no separate line column
    if (!col1 && col4) {
        console.log(`Special section (no line column) at row ${row + 1}: ${col4}`);

        // For special sections, use the full model as both line and model
        return {
            skip: false,
            rawLine: 'SPECIAL',
            rawModel: col4,
            isSpecialSection: true
        };
    }

    return {
        skip: false,
        rawLine: col1,
        rawModel: col4,
        isSpecialSection: false
    };
}

// Updated parsing functions based on training examples
function standardizePlasticName(rawPlastic, rawModel, vendorDescription = '') {
    if (!rawPlastic && !rawModel) return 'Unknown';

    const plastic = rawPlastic?.toString().trim() || '';
    const model = rawModel?.toString().trim() || '';

    // Handle fundraiser items first
    if (model.includes('Fundraiser - Ben Askren')) {
        const match = model.match(/Fundraiser - Ben Askren (.+?) (Thrasher|Buzzz|[A-Z][a-z]+)\s*\(/);
        if (match) {
            const plasticPart = match[1].trim(); // "Z Jawbreaker" or "Big Z"
            if (plasticPart.includes('Z Jawbreaker')) {
                return 'Elite Z Jawbreaker';
            } else if (plasticPart.includes('Big Z')) {
                return 'Big Z Collection';
            }
        }
        return 'Unknown'; // Fallback for unrecognized fundraiser items
    }

    // Handle special signature series first
    if (plastic.includes('Brodie Smith Z FLX')) {
        return 'Elite Z FLX Confetti';
    }

    if (plastic.includes('Anthony Barela Signature Series Z')) {
        return 'Elite Z';
    }

    // Handle SuperColor series (lines 401-414)
    if (plastic === 'SuperColor' || plastic.includes('SuperColor')) {
        // Check vendor description for Full Foil variants
        if (vendorDescription && vendorDescription.toString().toLowerCase().includes('full foil')) {
            return 'ESP Full Foil SuperColor';
        }
        return 'ESP SuperColor';
    }

    // Handle Tour Series (2025 Tour Series discs)
    if (plastic === 'Tour') {
        return 'Elite Z Swirl';
    }

    // Handle special cases
    if (plastic.includes('Fuzed') || plastic === 'Fuzed') {
        return 'Elite Z FuZed Line with Saw Pattern';
    }

    if (plastic === 'PM Z Lite') {
        return 'Elite Z Lite';
    }

    if (plastic === 'PP Z Lite') {
        return 'Elite Z Lite';
    }

    if (plastic === 'McBeth') {
        if (model && model.includes('NEW -')) {
            return 'ESP';
        }
        if (model && model.includes('1st run')) {
            return 'Jawbreaker/Rubber Blend';
        }
        if (model && model.includes('Big Z')) {
            return 'Big Z Collection';
        }
        if (model && model.includes('Z ') && !model.includes('Big Z')) {
            return 'Elite Z';
        }
        if (model && model.includes('Soft')) {
            return 'Putter Line Soft';
        }
        // Handle specific McBeth Hard Luna/Kratos parsing (line 162, 163)
        if (model && (model.includes('Hard Luna') || model.includes('Hard Kratos'))) {
            return 'Putter Line Hard';
        }
        // Handle McBeth Big Z special cases
        if (model && model.includes('Big Z')) {
            return 'Big Z Collection';
        }
        // Check if it's a regular McBeth signature disc (not putter line)
        const regularMolds = ['Anax', 'Athena', 'Hades', 'Luna', 'Malta', 'Zeus', 'Kratos'];
        if (regularMolds.some(mold => model.includes(mold))) {
            return 'ESP';
        }
        return 'Putter Line Hard';
    }

    if (plastic === 'Pierce') {
        if (model && model.includes('NEW -')) {
            return 'ESP';
        }
        // Handle Pierce Fierce (Swirl) parsing FIRST (line 323) - includes Soft Fierce
        if (model && model.includes('Fierce') && !model.includes('Hard')) {
            return 'Swirl';
        }
        // Handle specific Pierce Hard Fierce parsing (line 164)
        if (model && model.includes('Hard Fierce')) {
            return 'Putter Line Hard';
        }
        if (model && model.includes('Soft')) {
            return 'Putter Line Soft';
        }
        // Check if it's a regular Pierce signature disc (not putter line)
        const regularMolds = ['Drive', 'Passion', 'Zone'];
        if (regularMolds.some(mold => model.includes(mold))) {
            return 'ESP';
        }
        return 'Putter Line Hard';
    }

    if (plastic.includes('McBeth') && plastic.includes('FD')) {
        if (model.includes('Fly Dye')) {
            return 'Elite Z Fly-Dyed';
        }
        return 'ESP';
    }

    if (plastic.includes('Pierce') && plastic.includes('FD')) {
        if (model.includes('Fly Dye')) {
            return 'Elite Z Fly-Dyed';
        }
        return 'ESP';
    }

    // Handle Pierce FD parsing (Pierce FD|Fly Dye Z Passion|167-169g)
    if (plastic === 'Pierce FD' && model.includes('Fly Dye Z')) {
        return 'Elite Z Fly-Dyed';
    }

    // Handle Hard/Soft putter line parsing (Hard|Roach|170-172g, Soft|Focus|173-174g)
    // Also handles Hard Challenger OS/SS (line 369) and Soft Ringer GT (line 383)
    if (plastic === 'Hard') {
        return 'Putter Line Hard';
    }

    if (plastic === 'Soft') {
        return 'Putter Line Soft';
    }

    // Handle Big Z Collection parsing (Big Z|Venom|167-169g)
    if (plastic === 'Big Z') {
        return 'Big Z Collection';
    }

    // Handle Barela Swirl CT parsing (Barela|Swirl CT Focus|167-169g)
    if (plastic === 'Barela' && model.includes('Swirl CT')) {
        return 'Crazy Tough CT Swirl';
    }

    // Handle Z Glo parsing (Z Glo|Buzzz SS|167-169g)
    if (plastic === 'Z Glo') {
        return 'Seasonal Glow Elite Z';
    }

    // Handle Titanium parsing (Titanium|Scorch|160-166g, Titanium|Zone GT|line 398)
    if (plastic === 'Titanium') {
        return 'Ti Blend Titanium';
    }

    // Handle Brodie parsing
    if (plastic === 'Brodie') {
        if (model && model.includes('Swirl Roach')) {
            return 'Soft Swirl';
        }
        if (model && model.includes('FLX Zone')) {
            return 'Elite Z FLX Confetti';
        }
        return 'Unknown'; // Fallback for other Brodie products
    }

    // Standard mappings
    const plasticMappings = {
        'Z': 'Elite Z',
        'Z Lite': 'Elite Z Lite',
        'Z Fly Dye': 'Elite Z Fly-Dyed',
        'Z Glo': 'Elite Z Glow',
        'Jawbreaker': 'Jawbreaker',
        'X': 'Elite X',
        'ESP': 'ESP',
        'Recycle ESP': 'Recycled ESP',
        'RW Z': 'Elite Z',
        'RW ESP': 'ESP',
        'PP Z': 'Elite Z',
        'PM ESP': 'ESP',
        'PP ESP': 'ESP',
        'PS Z': 'Elite Z'
    };

    return plasticMappings[plastic] || plastic || 'Unknown';
}

function parseMoldAndStamp(rawModel, rawPlastic, vendorDescription = '') {
    const model = rawModel?.toString().trim() || '';
    const plastic = rawPlastic?.toString().trim() || '';
    const vendorDesc = vendorDescription?.toString().trim() || '';

    // Handle fundraiser items first
    if (model.includes('Fundraiser - Ben Askren')) {
        const match = model.match(/Fundraiser - Ben Askren (.+?) (Thrasher|Buzzz|[A-Z][a-z]+)\s*\(/);
        if (match) {
            const moldName = match[2]; // "Thrasher" or "Buzzz"
            return {
                mold_name: moldName,
                stamp_name: 'Live Free - Be Happy - Funky Ben Askren Fundraiser'
            };
        }
    }

    // Handle special signature series
    if (plastic.includes('Brodie Smith Z FLX Zone Confetti')) {
        return {
            mold_name: 'Zone',
            stamp_name: 'Get Freaky Dark Horse Brodie Smith'
        };
    }

    // Handle Brodie parsing (Brodie|Swirl Roach (new stamp)|160-166g)
    if (plastic === 'Brodie' && model.includes('Swirl Roach')) {
        return {
            mold_name: 'Roach',
            stamp_name: 'Brodie Smith - DarkHorse Scoober Bottom'
        };
    }

    // Handle Brodie FLX Zone parsing (Brodie|FLX Zone|167-169g)
    if (plastic === 'Brodie' && model.includes('FLX Zone')) {
        return {
            mold_name: 'Zone',
            stamp_name: 'Get Freaky Dark Horse Brodie Smith'
        };
    }

    if (plastic.includes('Anthony Barela Signature Series Z')) {
        // Extract mold from plastic line since model is NO_MODEL
        const moldFromPlastic = plastic.replace('Anthony Barela Signature Series Z', '').trim();
        return {
            mold_name: moldFromPlastic || extractMoldName(model),
            stamp_name: 'Anthony Barela Signature - AB'
        };
    }

    // Handle discontinued/retired products
    if (model.includes('Discontinued -')) {
        return {
            mold_name: model.replace(/.*Discontinued\s*-\s*/, '').trim(),
            stamp_name: 'Stock'
        };
    }

    if (model.includes('Retired stamp -')) {
        return {
            mold_name: model.replace(/.*Retired stamp\s*-\s*/, '').trim(),
            stamp_name: 'Stock'
        };
    }

    // Handle Fuzed with saw pattern
    if (plastic === 'Fuzed' && model.includes('Saw Pattern')) {
        return {
            mold_name: extractMoldName(model.replace(/\s*-\s*Saw Pattern/, '')),
            stamp_name: 'Big Bee with Saw Blade - "Buzzzsaw"'
        };
    }

    // Handle PM/PP Z Lite
    if (plastic === 'PM Z Lite') {
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'PM Logo Stock Stamp'
        };
    }

    if (plastic === 'PP Z Lite') {
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'PP 29190 5X Paige Pierce World Champion'
        };
    }

    // Handle specific McBeth Hard Luna/Kratos parsing (line 162, 163)
    if (plastic === 'McBeth' && model && (model.includes('Hard Luna') || model.includes('Hard Kratos'))) {
        const moldName = model.includes('Hard Luna') ? 'Luna' : 'Kratos';
        return {
            mold_name: moldName,
            stamp_name: 'PM Logo Stock Stamp'
        };
    }

    // Handle McBeth Big Z special cases
    if (plastic === 'McBeth' && model && model.includes('Big Z')) {
        const moldName = extractMoldName(model.replace('Big Z ', ''));
        return {
            mold_name: moldName,
            stamp_name: 'Big Z Stock Stamp with Inside Rim Embossed PM Paul McBeth'
        };
    }

    // Handle McBeth signature lines
    if (plastic === 'McBeth') {
        if (model && model.includes('NEW -')) {
            // For McBeth NEW products, ALL should use "Dye Line Blank Top Bottom" stamp (lines 269-275)
            const moldName = extractMoldName(model.replace('NEW - ', ''));

            return {
                mold_name: moldName,
                stamp_name: 'Dye Line Blank Top Bottom'
            };
        }
        if (model && model.includes('1st run Kratos')) {
            return {
                mold_name: 'Kratos',
                stamp_name: 'First Run with PM Logo'
            };
        }

        // Regular McBeth signature discs
        return {
            mold_name: extractMoldName(model.replace(/^Z\s+/, '')), // Remove Z prefix if present
            stamp_name: 'PM Logo Stock Stamp'
        };
    }

    // Handle Pierce Fierce (Swirl) parsing FIRST (line 323) - includes Soft Fierce
    if (plastic === 'Pierce' && model && model.includes('Fierce') && !model.includes('Hard')) {
        return {
            mold_name: 'Fierce',
            stamp_name: 'PP Logo Stock Stamp'
        };
    }

    // Handle specific Pierce Hard Fierce parsing (line 164)
    if (plastic === 'Pierce' && model && model.includes('Hard Fierce')) {
        return {
            mold_name: 'Fierce',
            stamp_name: 'PP Logo Stock Stamp'
        };
    }

    // Handle Pierce signature lines
    if (plastic === 'Pierce') {
        if (model && model.includes('NEW -')) {
            // For Pierce NEW products, ALL should use "Dye Line Blank Top Bottom" stamp (line 275)
            const moldName = extractMoldName(model.replace('NEW - ', ''));

            return {
                mold_name: moldName,
                stamp_name: 'Dye Line Blank Top Bottom'
            };
        }
        if (model && model.includes('Drive') && vendorDesc.includes('Paige Pierce Drive')) {
            return {
                mold_name: 'Drive',
                stamp_name: 'Paige Pierce - PP Logo - ZigZag Pattern'
            };
        }
        // Regular Pierce signature discs
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'PP Logo Stock Stamp'
        };
    }

    // Handle Recycle ESP
    if (plastic === 'Recycle ESP') {
        return {
            mold_name: extractMoldName(model),
            stamp_name: '100% Recycled ESP Stock'
        };
    }

    // Handle RW Z signature series
    if (plastic === 'RW Z') {
        return {
            mold_name: extractMoldName(model.replace(/^RW\s+/, '')),
            stamp_name: 'Ricky Wysocki - 2x World Champion Signature'
        };
    }

    // Handle PP Z signature series
    if (plastic === 'PP Z') {
        return {
            mold_name: extractMoldName(model.replace(/^PP\s+/, '')),
            stamp_name: 'PP 29190 5X Paige Pierce World Champion'
        };
    }

    // Handle PM ESP signature series
    if (plastic === 'PM ESP') {
        return {
            mold_name: extractMoldName(model.replace(/^PM\s+/, '')),
            stamp_name: 'Paul McBeth - 6x World Champion Signature'
        };
    }

    // Handle RW ESP signature series (RW ESP|RW Raptor|170-172g)
    if (plastic === 'RW ESP') {
        return {
            mold_name: extractMoldName(model.replace(/^RW\s+/, '')),
            stamp_name: 'Ricky Wysocki - 2x World Champion Signature'
        };
    }

    // Handle PP ESP signature series
    if (plastic === 'PP ESP') {
        return {
            mold_name: extractMoldName(model.replace(/^PP\s+/, '')),
            stamp_name: 'PP 29190 5X Paige Pierce World Champion'
        };
    }

    // Handle Tour Series (2025 Tour Series discs)
    if (plastic === 'Tour') {
        const tourSeriesMapping = {
            'Buzzz': 'Chris Dickerson 2025 Tour Series',
            'BuzzzSS': 'Ezra Robinson 2025 Tour Series',
            'Force': 'Ricky Wysocki 2025 Tour Series',
            'Vulture': 'Holyn Handley 2025 Tour Series',
            'Zone': 'Adam Hammes 2025 Tour Series',
            'Nuke': 'Ezra Aderhold 2025 Tour Series',
            'Thrasher': 'Missy Gannon 2025 Tour Series',
            'Swarm': 'Andrew Presnell 2025 Tour Series',
            'Scorch': 'Valerie Mandujano 2025 Tour Series',
            'Raptor': 'Aaron Gossage 2025 Tour Series',
            'Venom': 'Anthony Barela 2025 Tour Series',
            'Drive': 'Paige Pierce 2025 Tour Series',
            'Luna': 'Paul McBeth 2025 Tour Series'
        };

        const moldName = extractMoldName(model);
        const tourStamp = tourSeriesMapping[moldName];

        return {
            mold_name: moldName,
            stamp_name: tourStamp || 'Stock' // Fallback for non-Discraft molds
        };
    }



    // Handle signature series stamps (legacy)
    if (plastic.includes('McBeth') || model.includes('McBeth')) {
        if (plastic.includes('FD')) {
            return {
                mold_name: extractMoldName(model),
                stamp_name: 'Paul McBeth Large Signature'
            };
        }
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'PM Logo Stock Stamp'
        };
    }

    // Handle Pierce FD stamp parsing (Pierce FD|Fly Dye Z Passion|167-169g) - SPECIFIC CASE FIRST
    if (plastic === 'Pierce FD' && model.includes('Fly Dye Z Passion')) {
        return {
            mold_name: 'Passion',
            stamp_name: 'PP Logo Stock Stamp'
        };
    }

    if (plastic.includes('Pierce') || model.includes('Pierce')) {
        if (plastic.includes('FD')) {
            return {
                mold_name: extractMoldName(model),
                stamp_name: 'Paul McBeth Large Signature'
            };
        }
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'PP Logo Stock Stamp'
        };
    }

    // Handle special stamps
    if (model.includes('PS Z Buzzz SS')) {
        return {
            mold_name: 'BuzzzSS',
            stamp_name: 'Paige Shue - 2018 World Champion'
        };
    }

    // Handle PS Z parsing (PS Z|PS Buzzz SS|170-172g)
    if (plastic === 'PS Z' && model.includes('PS Buzzz SS')) {
        return {
            mold_name: 'BuzzzSS',
            stamp_name: 'Paige Shue - 2018 World Champion'
        };
    }

    // Handle Hard putter line parsing (line 369: Hard Challenger OS/SS)
    if (plastic === 'Hard') {
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'Stock'
        };
    }

    // Handle Soft putter line parsing (line 383: Soft Ringer GT)
    if (plastic === 'Soft') {
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'Stock'
        };
    }

    // Handle SuperColor special parsing (lines 401-414)
    if (plastic === 'SuperColor') {
        // For SuperColor discs, use vendor description to determine stamp
        if (vendorDescription) {
            const desc = vendorDescription.toString().toLowerCase();

            // SuperColor Paul McBeth Luna
            if (desc.includes('supercolor paul mcbeth luna')) {
                return {
                    mold_name: 'Luna',
                    stamp_name: 'Luna Character'
                };
            }

            // SuperColor Gallery Buzzz variants
            if (desc.includes('supercolor gallery buzzz')) {
                if (desc.includes('bali')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Bali' };
                if (desc.includes('bunksy')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Bunksy' };
                if (desc.includes('demise')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Demise' };
                if (desc.includes('earth')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Earth' };
                if (desc.includes('fire')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Nebula Fire' };
                if (desc.includes('ancient alien')) return { mold_name: 'Buzzz', stamp_name: 'Brian Allen Ancient Alien' };
                if (desc.includes('astronaut')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Astronaut' };
                if (desc.includes('owl')) return { mold_name: 'Buzzz', stamp_name: 'Brian Allen Owl' };
                if (desc.includes('lichten')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Lichten' };
                if (desc.includes('moon')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Moon' };
            }

            // Full Foil SuperColor variants
            if (desc.includes('full foil supercolor')) {
                if (desc.includes('chains green')) return { mold_name: 'Buzzz', stamp_name: 'Chains Green' };
                if (desc.includes('chains pink')) return { mold_name: 'Buzzz', stamp_name: 'Chains Pink' };
                if (desc.includes('chains blue')) return { mold_name: 'Buzzz', stamp_name: 'Chains Blue' };
            }
        }

        // Default SuperColor parsing if no specific match
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'SuperColor'
        };
    }

    // Handle Titanium Zone GT parsing (line 398)
    if (plastic === 'Titanium' && model && model.includes('Zone GT')) {
        return {
            mold_name: 'Zone GT',
            stamp_name: 'Stock'
        };
    }

    // Handle Barela Swirl CT parsing (Barela|Swirl CT Focus|167-169g)
    if (plastic === 'Barela' && model.includes('Swirl CT')) {
        return {
            mold_name: extractMoldName(model.replace(/Swirl CT\s+/, '')),
            stamp_name: 'Anthony Barela Signature - AB'
        };
    }



    if (model.includes('RW Z Nuke')) {
        return {
            mold_name: 'Nuke',
            stamp_name: 'Ricky Wysocki - 2x World Champion Signature'
        };
    }

    // Legacy PP Z handling (for cases not caught above)
    if (model.includes('PP Z')) {
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'PP 29190 5X Paige Pierce World Champion'
        };
    }

    // Handle ESP products with White/Blank stamp (lines 276-281)
    if (plastic === 'ESP') {
        // Check if vendor description contains White/Blank pattern
        if (vendorDescription && vendorDescription.toLowerCase().includes('white/blank top/bottom stamp')) {
            return {
                mold_name: extractMoldName(model),
                stamp_name: 'Dye Line Blank Top Bottom'
            };
        }
        // Default ESP stamp
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'Stock'
        };
    }

    return {
        mold_name: extractMoldName(model),
        stamp_name: 'Stock'
    };
}

function extractMoldName(model) {
    if (!model) return 'Unknown';

    const modelStr = model.toString().trim();

    // Handle special cases first
    if (modelStr.includes('Cigarra')) return 'Cigarra';
    if (modelStr.includes('Buzzz SS')) return 'BuzzzSS';
    if (modelStr.includes('Surge SS')) return 'Surge SS';
    if (modelStr.includes('Avenger SS')) return 'Avenger SS';
    if (modelStr.includes('Crank SS')) return 'CrankSS';
    if (modelStr.includes('Buzzz OS')) return 'BuzzzOS';
    if (modelStr.includes('Nuke OS')) return 'NukeOS';
    if (modelStr.includes('Nuke SS')) return 'Nuke SS';
    if (modelStr.includes('Zone OS')) return 'Zone OS';
    if (modelStr.includes('Banger GT')) return 'Banger GT';
    if (modelStr.includes('Challenger OS')) return 'Challenger OS';
    if (modelStr.includes('Challenger SS')) return 'Challenger SS';
    if (modelStr.includes('Ringer GT')) return 'Ringer GT';

    // Remove common prefixes and suffixes
    let mold = modelStr
        .replace(/^(Fly Dye Z|Z Glo|Z Lite|Z|ESP|Jawbreaker|X)\s+/i, '')
        .replace(/\s+(Discontinued|Retired stamp|NEW).*$/i, '')
        .replace(/^(PS|RW|PP)\s+(Z|ESP)\s+/i, '')
        .replace(/^Soft\s+/i, '') // Remove "Soft" prefix
        .replace(/^Hard\s+/i, '') // Remove "Hard" prefix
        .replace(/^Big Z\s+/i, '') // Remove "Big Z" prefix
        .trim();

    // Handle "NEW -" prefix
    if (mold.startsWith('NEW -')) {
        mold = mold.replace(/^NEW\s*-\s*/, '').trim();
    }

    // Handle discontinued/retired patterns
    if (mold.includes('Discontinued -')) {
        mold = mold.replace(/.*Discontinued\s*-\s*/, '').trim();
    }

    if (mold.includes('Retired stamp -')) {
        mold = mold.replace(/.*Retired stamp\s*-\s*/, '').trim();
    }

    // Extract first word as mold name, but handle compound names
    const words = mold.split(/\s+/);

    // Handle compound mold names
    if (words.length >= 2) {
        const firstTwo = `${words[0]} ${words[1]}`;
        if (firstTwo === 'Buzzz SS') return 'BuzzzSS';
        if (firstTwo === 'Buzzz OS') return 'BuzzzOS';
        if (firstTwo === 'Crank SS') return 'CrankSS';
        if (firstTwo === 'Nuke OS') return 'NukeOS';
        if (['Surge SS', 'Avenger SS', 'Nuke SS', 'Zone OS', 'Banger GT'].includes(firstTwo)) {
            return firstTwo;
        }
    }

    // Return first word
    return words[0] || 'Unknown';
}

function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

async function importFullDiscraftFile() {
    try {
        console.log('🚀 Starting FULL import of Discraft order sheet...');
        
        const filePath = path.join(__dirname, 'data', 'external data', 'discraftstock.xlsx');
        console.log(`📁 Reading Excel file: ${filePath}`);
        
        // Calculate file hash for change detection
        const fileHash = calculateFileHash(filePath);
        console.log(`📊 File hash: ${fileHash}`);
        
        // Read the Excel file
        const workbook = XLSX.readFile(filePath);
        const sheetName = 'Order Form';
        const worksheet = workbook.Sheets[sheetName];
        
        if (!worksheet) {
            throw new Error(`Sheet "${sheetName}" not found`);
        }
        
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        console.log(`📊 Sheet range: ${XLSX.utils.encode_range(range)} (${range.e.r + 1} rows)`);
        
        const importData = [];
        const batchId = generateUUID();

        // Track current weight headers for dynamic parsing
        let currentWeightHeaders = null;

        // Process ALL rows in the sheet
        for (let row = 0; row <= range.e.r; row++) {
            if (row % 50 === 0) {
                console.log(`📊 Processing row ${row + 1}/${range.e.r + 1}...`);
            }
            
            const rowData = {};
            
            // Read all columns for this row
            for (let col = 0; col <= range.e.c; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                const cell = worksheet[cellAddress];
                if (cell && cell.v !== null && cell.v !== undefined && cell.v !== '') {
                    rowData[`col_${col}`] = cell.v;
                }
            }
            
            // Parse special sections and handle skips
            const sectionInfo = parseSpecialSections(rowData, row);
            if (sectionInfo.skip) {
                if (sectionInfo.stopImport) {
                    console.log(`🛑 Stopping import at row ${row + 1} due to McBeth Apparel section`);
                    break; // Stop processing all remaining rows
                }
                continue;
            }
            
            const rawPlastic = sectionInfo.rawLine;
            const rawModel = sectionInfo.rawModel;
            
            // Skip if no meaningful content
            if (!rawPlastic && !rawModel) {
                continue;
            }
            
            // Check if this is a header row and extract weight headers
            if (rawPlastic === 'Line' && rawModel === 'Model') {
                console.log(`📋 Found header row at ${row + 1}, scanning for weight headers...`);

                // Convert rowData to array format for findWeightHeaders
                const rowArray = [];
                for (let col = 0; col <= range.e.c; col++) {
                    rowArray[col] = rowData[`col_${col}`];
                }

                // Find weight headers in this row
                const weightHeaders = findWeightHeaders(rowArray);

                if (Object.keys(weightHeaders).length > 0) {
                    currentWeightHeaders = weightHeaders;
                    console.log(`   Found ${Object.keys(weightHeaders).length} weight columns:`);
                    Object.entries(weightHeaders).forEach(([colIndex, weightInfo]) => {
                        console.log(`     Column ${weightInfo.letter} (${colIndex}): ${weightInfo.name}`);
                    });
                } else {
                    console.log(`   No weight headers found, using fallback mappings`);
                    currentWeightHeaders = null;
                }

                continue;
            }
            
            // Check multiple columns for vendor description (SuperColor info is in M-AA columns, which are 12-26)
            // Also include column T (19) for McBeth NEW products and Pierce Drive
            // Skip "N/A", "out", and empty values to find the actual vendor description
            let vendorDesc = '';
            const vendorColumns = [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26];
            for (const col of vendorColumns) {
                const value = rowData[`col_${col}`];
                if (value && value !== 'N/A' && value !== 'n/a' && value !== 'NA' &&
                    value !== 'out' && value !== 'OUT' && value !== '') {
                    vendorDesc = value.toString();
                    break;
                }
            }
            const plasticName = standardizePlasticName(rawPlastic, rawModel, vendorDesc);
            const { mold_name, stamp_name } = parseMoldAndStamp(rawModel, rawPlastic, vendorDesc);

            // Handle fundraiser items specially - they go in column A, not weight columns
            if (rawModel && rawModel.includes('Fundraiser - Ben Askren')) {
                // Skip header rows for fundraiser sections
                if (rawPlastic && rawPlastic.includes('LIMITED RELEASE - Fundraiser')) {
                    continue; // Skip header rows
                }

                // Check if there's an "Order qty" cell in column A (index 0)
                const orderQtyStatus = rowData[`col_0`];
                if (orderQtyStatus && orderQtyStatus.toString().toLowerCase().includes('order qty')) {
                    // This is the actual fundraiser product row
                    const weightData = { name: 'Order Qty', min: 150, max: 180, letter: 'A' };
                    const mappingKey = createProductKey(rawPlastic, rawModel, weightData.name);

                    const productRecord = {
                        mold_name: mold_name,
                        plastic_name: plasticName,
                        min_weight: weightData.min,
                        max_weight: weightData.max,
                        color_name: 'Varies',
                        stamp_name: stamp_name,
                        vendor_product_code: `${plasticName}_${mold_name}_${weightData.min}-${weightData.max}`.replace(/\s+/g, '_'),
                        is_orderable: true,
                        is_currently_available: true,
                        cost_price: 10.00, // Extract from description if available
                        excel_mapping_key: mappingKey,
                        excel_row_hint: row + 1,
                        excel_column: weightData.letter,
                        raw_line_type: rawPlastic,
                        raw_model: rawModel,
                        raw_weight_range: weightData.name,
                        import_file_hash: fileHash,
                        import_batch_id: batchId,
                        vendor_description: vendorDesc,
                        raw_status: orderQtyStatus
                    };

                    // Extract pricing if available from raw model
                    const costMatch = rawModel.match(/cost\s*\$?(\d+\.?\d*)/i);
                    if (costMatch) {
                        productRecord.cost_price = parseFloat(costMatch[1]);
                    }

                    importData.push(productRecord);
                }
                continue; // Skip normal weight column processing for fundraiser items
            }

            // Check if this is a product that should use Assorted weight (160-180g range)
            let hasAssortedWeight = false;
            let assortedStatus = null;

            // Tour Series and SuperColor products should be consolidated to single 160-180g range
            if (rawPlastic === 'Tour' || rawPlastic === 'SuperColor') {
                hasAssortedWeight = true;
                // For assorted products, get status from any weight column that has data
                // Use dynamic weight headers if available, otherwise scan all columns
                const columnsToCheck = currentWeightHeaders ?
                    Object.keys(currentWeightHeaders).map(col => parseInt(col)) :
                    [12, 13, 14, 15, 16, 17, 18];

                for (const col of columnsToCheck) {
                    const cellValue = rowData[`col_${col}`];
                    if (cellValue && cellValue !== null && cellValue !== '') {
                        assortedStatus = cellValue;
                        break;
                    }
                }
            }

            if (hasAssortedWeight) {
                // Handle Assorted weight as single 160-180g range
                const weightData = getAssortedWeightRange();

                // Determine availability based on status indicators (if present)
                let isCurrentlyAvailable = false;
                let isOrderable = true;

                if (assortedStatus !== undefined && assortedStatus !== null && assortedStatus !== '') {
                    const statusStr = assortedStatus.toString().toLowerCase();
                    isCurrentlyAvailable = (statusStr.includes('9') || statusStr === '9');
                    isOrderable = (statusStr.includes('9') || statusStr === '9' || statusStr.includes('out'));
                } else {
                    isCurrentlyAvailable = false;
                    isOrderable = true;
                }

                // Determine color and stamp for special cases
                let colorName = 'Varies';
                let finalStampName = stamp_name;

                // Handle ESP White special case
                let vendorDescForWhite = '';
                const vendorColumns = [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26];
                for (const col of vendorColumns) {
                    const value = rowData[`col_${col}`];
                    if (value && value !== 'N/A' && value !== 'n/a' && value !== 'NA' &&
                        value !== 'out' && value !== 'OUT' && value !== '') {
                        vendorDescForWhite = value.toString();
                        break;
                    }
                }
                if (vendorDescForWhite.includes('White/Blank Top/Bottom stamp')) {
                    colorName = 'White';
                    finalStampName = 'Dye Line Blank Top Bottom';
                }

                // Create robust mapping key
                const mappingKey = createProductKey(rawPlastic, rawModel, weightData.name);

                const productRecord = {
                    mold_name: mold_name,
                    plastic_name: plasticName,
                    min_weight: weightData.min,
                    max_weight: weightData.max,
                    color_name: colorName,
                    stamp_name: finalStampName,
                    vendor_product_code: `${plasticName}_${mold_name}_${weightData.min}-${weightData.max}`.replace(/\s+/g, '_'),
                    is_orderable: isOrderable,
                    is_currently_available: isCurrentlyAvailable,
                    cost_price: null,

                    // ROBUST MAPPING - Content-based key
                    excel_mapping_key: mappingKey,
                    excel_row_hint: row + 1,
                    excel_column: weightData.letter,

                    // Raw content for validation and rebuilding
                    raw_line_type: rawPlastic,
                    raw_model: rawModel,
                    raw_weight_range: weightData.name,

                    // File tracking
                    import_file_hash: fileHash,
                    import_batch_id: batchId,
                    vendor_description: vendorDescForWhite,

                    // Status tracking for debugging
                    raw_status: assortedStatus
                };

                // Extract pricing if available
                const description = productRecord.vendor_description || '';
                const costMatch = description.match(/cost\s*\$?(\d+\.?\d*)/i);
                if (costMatch) {
                    productRecord.cost_price = parseFloat(costMatch[1]);
                }

                importData.push(productRecord);

            } else {
                // Import ONLY weight ranges that have actual status data
                // Use dynamic weight headers if available, otherwise scan all columns
                const columnsToCheck = currentWeightHeaders ?
                    Object.keys(currentWeightHeaders).map(col => parseInt(col)) :
                    [12, 13, 14, 15, 16, 17, 18];

                for (const col of columnsToCheck) {
                    const weightData = getWeightRange(col, currentWeightHeaders);
                    if (!weightData) continue;

                    const status = rowData[`col_${col}`];

                    // Determine availability based on status indicators
                    let isCurrentlyAvailable = false;
                    let isOrderable = true;

                    if (status === undefined || status === null || status === '') {
                        // Blank cells = available and orderable
                        isCurrentlyAvailable = true;
                        isOrderable = true;
                    } else if (status === 'N/A' || status === 'n/a' || status === 'NA') {
                        // N/A = not available for this weight range, skip entirely
                        continue;
                    } else {
                        // Status indicators present - use them
                        isCurrentlyAvailable = (status === 9 || status === '9');
                        isOrderable = (status === 9 || status === '9' ||
                                     (status && status.toString().toLowerCase() === 'out'));
                    }

                // Determine color and stamp for special cases
                let colorName = 'Varies';
                let finalStampName = stamp_name;

                // Handle ESP White special case
                let vendorDescForWhite2 = '';
                const vendorColumns2 = [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26];
                for (const col of vendorColumns2) {
                    const value = rowData[`col_${col}`];
                    if (value && value !== 'N/A' && value !== 'n/a' && value !== 'NA' &&
                        value !== 'out' && value !== 'OUT' && value !== '') {
                        vendorDescForWhite2 = value.toString();
                        break;
                    }
                }
                if (vendorDescForWhite2.includes('White/Blank Top/Bottom stamp')) {
                    colorName = 'White';
                    finalStampName = 'Dye Line Blank Top Bottom';
                }

                // Create robust mapping key
                const mappingKey = createProductKey(rawPlastic, rawModel, weightData.name);

                const productRecord = {
                    mold_name: mold_name,
                    plastic_name: plasticName,
                    min_weight: weightData.min,
                    max_weight: weightData.max,
                    color_name: colorName,
                    stamp_name: finalStampName,
                    vendor_product_code: `${plasticName}_${mold_name}_${weightData.min}-${weightData.max}`.replace(/\s+/g, '_'),
                    is_orderable: isOrderable,
                    is_currently_available: isCurrentlyAvailable,
                    cost_price: null,

                    // ROBUST MAPPING - Content-based key
                    excel_mapping_key: mappingKey,
                    excel_row_hint: row + 1,
                    excel_column: weightData.letter,

                    // Raw content for validation and rebuilding
                    raw_line_type: rawPlastic,
                    raw_model: rawModel,
                    raw_weight_range: weightData.name,

                    // File tracking
                    import_file_hash: fileHash,
                    import_batch_id: batchId,
                    vendor_description: vendorDescForWhite2,

                    // Status tracking for debugging
                    raw_status: status
                };

                // Extract pricing if available
                const description = productRecord.vendor_description || '';
                const costMatch = description.match(/cost\s*\$?(\d+\.?\d*)/i);
                if (costMatch) {
                    productRecord.cost_price = parseFloat(costMatch[1]);
                }

                importData.push(productRecord);
                }
            }
        }
        
        console.log(`\n📊 Parsed ${importData.length} orderable products from ${range.e.r + 1} rows`);
        
        if (importData.length === 0) {
            console.log('❌ No orderable products found in the file');
            return;
        }
        
        // Import to database
        console.log('\n💾 Importing to database...');
        await importToDatabase(importData, batchId);
        
    } catch (error) {
        console.error('❌ Error in full import:', error);
        throw error;
    }
}

async function importToDatabase(records, batchId) {
    try {
        // Clear existing data (TRUNCATE AND REPLACE strategy)
        console.log('🧹 Clearing existing data...');
        const { error: deleteError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .delete()
            .neq('id', 0); // Delete all records
        
        if (deleteError) {
            console.warn('Warning: Could not clear existing data:', deleteError.message);
        }
        
        // Insert new data in batches
        console.log(`📥 Inserting ${records.length} records in batches...`);
        
        const batchSize = 100;
        let insertedCount = 0;
        
        for (let i = 0; i < records.length; i += batchSize) {
            const batch = records.slice(i, i + batchSize);
            
            const { data, error } = await supabase
                .from('it_discraft_order_sheet_lines')
                .insert(batch)
                .select('id');
            
            if (error) {
                console.error(`❌ Error inserting batch ${Math.floor(i/batchSize) + 1}:`, error);
                throw error;
            }
            
            insertedCount += batch.length;
            console.log(`✅ Inserted batch ${Math.floor(i/batchSize) + 1}: ${insertedCount}/${records.length} records`);
        }
        
        console.log(`✅ Successfully imported ${records.length} records`);
        console.log(`📦 Import batch ID: ${batchId}`);
        
        // Show summary
        await showImportSummary();

        console.log('\n✅ Import completed successfully!');
        console.log('💡 Use the admin interface to calculate MPS IDs for matching.');

    } catch (error) {
        console.error('❌ Error importing to database:', error);
        throw error;
    }
}

async function showImportSummary() {
    try {
        // Get all records using pagination to avoid Supabase limits
        let allRecords = [];
        let from = 0;
        const pageSize = 1000;

        while (true) {
            const { data: batch, error } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('plastic_name, mold_name, is_currently_available')
                .range(from, from + pageSize - 1);

            if (error) {
                console.warn('Could not generate summary:', error.message);
                return;
            }

            if (batch.length === 0) break;

            allRecords = allRecords.concat(batch);
            from += pageSize;

            if (batch.length < pageSize) break; // Last page
        }

        console.log(`📊 Summary query returned ${allRecords.length} records`);
        const summary = allRecords;
        
        console.log('\n📊 Import Summary:');
        
        const byPlastic = {};
        const byMold = {};
        let availableCount = 0;
        let outOfStockCount = 0;
        
        summary.forEach(record => {
            byPlastic[record.plastic_name] = (byPlastic[record.plastic_name] || 0) + 1;
            byMold[record.mold_name] = (byMold[record.mold_name] || 0) + 1;
            
            if (record.is_currently_available) {
                availableCount++;
            } else {
                outOfStockCount++;
            }
        });
        
        console.log('\n🎯 By Plastic Type:');
        Object.entries(byPlastic)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .forEach(([plastic, count]) => {
                console.log(`  ${plastic}: ${count} products`);
            });
        
        console.log('\n🥏 By Mold (Top 15):');
        Object.entries(byMold)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 15)
            .forEach(([mold, count]) => {
                console.log(`  ${mold}: ${count} products`);
            });
        
        // Get accurate total count
        const { count: totalCount, error: countError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true });

        console.log('\n📈 By Availability:');
        console.log(`  ✅ Available now: ${availableCount} products`);
        console.log(`  ❌ Out of stock: ${outOfStockCount} products`);
        console.log(`  📊 Total: ${countError ? summary.length : totalCount} products`);
        
    } catch (error) {
        console.warn('Error generating summary:', error.message);
    }
}

async function calculateMpsIds() {
    try {
        console.log('🧹 Resetting existing MPS IDs...');
        const { error: resetError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .update({ calculated_mps_id: null })
            .neq('id', 0);

        if (resetError) {
            console.error('❌ Error resetting MPS IDs:', resetError);
            return;
        }

        console.log('📊 Getting Discraft products...');
        // Get all products in chunks to avoid any limits
        let allProducts = [];
        let offset = 0;
        const chunkSize = 1000;

        while (true) {
            const { data: productChunk, error: discraftError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('id, plastic_name, mold_name, stamp_name')
                .range(offset, offset + chunkSize - 1);

            if (discraftError) {
                console.error('❌ Error getting Discraft products:', discraftError);
                return;
            }

            if (!productChunk || productChunk.length === 0) break;

            allProducts = allProducts.concat(productChunk);
            console.log(`   Retrieved ${allProducts.length} products so far...`);

            if (productChunk.length < chunkSize) break;
            offset += chunkSize;
        }

        const discraftProducts = allProducts;

        console.log('📊 Getting Discraft MPS records...');
        const { data: mpsRecords, error: mpsError } = await supabase
            .from('t_mps')
            .select(`
                id,
                t_plastics!inner(plastic, brand_id),
                t_molds!inner(mold, brand_id),
                t_stamps!inner(stamp)
            `)
            .eq('active', true)
            .eq('t_plastics.brand_id', 6)
            .eq('t_molds.brand_id', 6);

        if (mpsError) {
            console.error('❌ Error getting MPS records:', mpsError);
            return;
        }

        const mpsMap = new Map();
        mpsRecords.forEach(mps => {
            const key = `${mps.t_plastics.plastic.trim()}|${mps.t_molds.mold.trim()}|${mps.t_stamps.stamp.trim()}`;
            mpsMap.set(key, mps.id);
        });

        console.log('🔍 Matching products to MPS records...');
        let matchedCount = 0;

        for (const product of discraftProducts) {
            const key = `${product.plastic_name.trim()}|${product.mold_name.trim()}|${product.stamp_name.trim()}`;
            const mpsId = mpsMap.get(key);

            if (mpsId) {
                const { error } = await supabase
                    .from('it_discraft_order_sheet_lines')
                    .update({ calculated_mps_id: mpsId })
                    .eq('id', product.id);

                if (!error) {
                    matchedCount++;
                }
            }
        }

        const failedCount = discraftProducts.length - matchedCount;
        const successRate = ((matchedCount / discraftProducts.length) * 100).toFixed(2);

        console.log('✅ MPS ID calculation completed');
        console.log(`📊 Results:`);
        console.log(`   • Total products: ${discraftProducts.length}`);
        console.log(`   • Successfully matched: ${matchedCount}`);
        console.log(`   • Failed to match: ${failedCount}`);
        console.log(`   • Success rate: ${successRate}%`);

        if (successRate < 80) {
            console.log('⚠️ Low success rate - consider improving parsing logic');
        }

    } catch (error) {
        console.error('❌ Error calculating MPS IDs:', error);
    }
}

// Run the full import
importFullDiscraftFile()
    .then(() => {
        console.log('\n🎉 Full import completed successfully!');
        process.exit(0);
    })
    .catch((error) => {
        console.error('\n❌ Full import failed:', error);
        process.exit(1);
    });
