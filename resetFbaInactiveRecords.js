// Script to reset FBA inactive records
// Updates t_sdasins records based on v_sdasins_fba_inv0_mps_inactive view
// Sets fba='N', fbafnsku=null, fba_uploaded_at=null, min_weight=1, max_weight=2, and updates notes

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing required environment variables: SUPABASE_URL or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Reset FBA inactive records based on the view
 * @param {Object} supabaseClient - Optional Supabase client (for use from adminServer)
 * @returns {Object} Result object with success status and details
 */
export async function resetFbaInactiveRecords(supabaseClient = null) {
  const client = supabaseClient || supabase;
  
  try {
    console.log('🔄 Starting FBA inactive records reset...');
    
    // First, get the records from the view to see what we're working with
    console.log('📊 Querying v_sdasins_fba_inv0_mps_inactive view...');
    
    const { data: viewRecords, error: viewError } = await client
      .from('v_sdasins_fba_inv0_mps_inactive')
      .select('*');
    
    if (viewError) {
      console.error('❌ Error querying view:', viewError.message);
      return {
        success: false,
        error: `Failed to query view: ${viewError.message}`,
        recordsFound: 0,
        recordsUpdated: 0
      };
    }
    
    if (!viewRecords || viewRecords.length === 0) {
      console.log('ℹ️ No records found in v_sdasins_fba_inv0_mps_inactive view');
      return {
        success: true,
        message: 'No records found to update',
        recordsFound: 0,
        recordsUpdated: 0
      };
    }
    
    console.log(`📋 Found ${viewRecords.length} records in view`);
    
    // Extract the sdasin IDs from the view records
    // The view uses 'sdasin_id' as the identifier field
    const sdasinIds = viewRecords.map(record => record.sdasin_id);
    
    if (!sdasinIds || sdasinIds.length === 0) {
      console.log('🔍 View record structure:', Object.keys(viewRecords[0]));
      return {
        success: false,
        error: 'No valid sdasin IDs found in view records',
        recordsFound: viewRecords.length,
        recordsUpdated: 0,
        viewStructure: Object.keys(viewRecords[0])
      };
    }
    
    console.log(`🎯 Updating ${sdasinIds.length} t_sdasins records...`);
    
    // Get current timestamp for the notes
    const currentTimestamp = new Date().toISOString();
    
    // First, get the current notes for each record so we can prepend to them
    const { data: currentRecords, error: fetchError } = await client
      .from('t_sdasins')
      .select('id, notes')
      .in('id', sdasinIds);
    
    if (fetchError) {
      console.error('❌ Error fetching current records:', fetchError.message);
      return {
        success: false,
        error: `Failed to fetch current records: ${fetchError.message}`,
        recordsFound: viewRecords.length,
        recordsUpdated: 0
      };
    }
    
    // Update each record individually to handle the notes prepending
    let updatedCount = 0;
    const updateErrors = [];
    
    for (const record of currentRecords) {
      try {
        const currentNotes = record.notes || '';
        const newNotesPrefix = `Discontinued and Out of Stock MPS - Listing removed from Amazon on ${currentTimestamp}`;
        const updatedNotes = currentNotes ? `${newNotesPrefix}\n${currentNotes}` : newNotesPrefix;
        
        const { error: updateError } = await client
          .from('t_sdasins')
          .update({
            fba: 'N',
            fbafnsku: null,
            fba_uploaded_at: null,
            min_weight: 1,
            max_weight: 2,
            notes: updatedNotes
          })
          .eq('id', record.id);
        
        if (updateError) {
          console.error(`❌ Error updating record ${record.id}:`, updateError.message);
          updateErrors.push(`Record ${record.id}: ${updateError.message}`);
        } else {
          updatedCount++;
        }
      } catch (err) {
        console.error(`❌ Exception updating record ${record.id}:`, err.message);
        updateErrors.push(`Record ${record.id}: ${err.message}`);
      }
    }
    
    if (updateErrors.length > 0) {
      console.log(`⚠️ Some updates failed. Successfully updated: ${updatedCount}, Failed: ${updateErrors.length}`);
    }
    
    console.log(`✅ Successfully updated ${updatedCount} t_sdasins records`);
    console.log('📝 Changes made:');
    console.log('   - fba set to "N"');
    console.log('   - fbafnsku set to NULL');
    console.log('   - fba_uploaded_at set to NULL');
    console.log('   - min_weight set to 1');
    console.log('   - max_weight set to 2');
    console.log('   - notes prepended with discontinuation message');
    
    return {
      success: true,
      message: `Successfully updated ${updatedCount} t_sdasins records`,
      recordsFound: viewRecords.length,
      recordsUpdated: updatedCount,
      errors: updateErrors.length > 0 ? updateErrors : undefined,
      details: {
        changes: {
          fba: 'N',
          fbafnsku: 'NULL',
          fba_uploaded_at: 'NULL',
          min_weight: 1,
          max_weight: 2,
          notes: 'Prepended with discontinuation message'
        }
      }
    };
    
  } catch (error) {
    console.error('❌ Exception in resetFbaInactiveRecords:', error);
    return {
      success: false,
      error: error.message,
      recordsFound: 0,
      recordsUpdated: 0
    };
  }
}

// If running directly (not imported)
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const isMainModule = process.argv[1] === __filename;

if (isMainModule) {
  console.log('🚀 Running FBA inactive records reset script...');
  
  resetFbaInactiveRecords()
    .then(result => {
      if (result.success) {
        console.log(`\n✅ Script completed successfully!`);
        console.log(`📊 Records found: ${result.recordsFound}`);
        console.log(`🔄 Records updated: ${result.recordsUpdated}`);
        if (result.errors) {
          console.log(`⚠️ Errors encountered: ${result.errors.length}`);
        }
      } else {
        console.log(`\n❌ Script failed: ${result.error}`);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('\n💥 Script crashed:', error);
      process.exit(1);
    });
}

export default resetFbaInactiveRecords;
