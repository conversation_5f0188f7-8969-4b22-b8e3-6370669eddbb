import ExcelJS from 'exceljs';
import path from 'path';

async function checkExportedZeros() {
  try {
    console.log('🔍 Checking exported Excel file for 0 values...\n');
    
    const filePath = path.join(process.cwd(), 'data', 'external data', 'test_discraft_export_with_zeros_2025-07-05-22-07-42.xlsx');
    console.log(`📁 Reading file: ${filePath}`);
    
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);
    const worksheet = workbook.getWorksheet(1);
    
    console.log('📊 Checking for 0 values in order quantity cells...\n');
    
    let zeroCount = 0;
    let nonZeroCount = 0;
    let totalCellsChecked = 0;
    
    // Check a sample of rows around lines 285-300 (where we expect to see some 0s)
    const rowsToCheck = [285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300];
    const columnsToCheck = ['M', 'N', 'O', 'P', 'Q', 'R']; // Weight columns
    
    console.log('📋 Sample of cells checked:');
    
    for (const row of rowsToCheck) {
      for (const col of columnsToCheck) {
        const cellAddress = col + row;
        const cell = worksheet.getCell(cellAddress);
        const value = cell.value;
        
        totalCellsChecked++;
        
        if (value === 0) {
          zeroCount++;
          console.log(`   ${cellAddress}: 0 ✅`);
        } else if (value && value > 0) {
          nonZeroCount++;
          console.log(`   ${cellAddress}: ${value} 📦`);
        } else {
          // Empty or null
          console.log(`   ${cellAddress}: (empty)`);
        }
      }
    }
    
    console.log(`\n📊 Summary:`);
    console.log(`   • Total cells checked: ${totalCellsChecked}`);
    console.log(`   • Cells with 0: ${zeroCount}`);
    console.log(`   • Cells with orders > 0: ${nonZeroCount}`);
    console.log(`   • Empty cells: ${totalCellsChecked - zeroCount - nonZeroCount}`);
    
    if (zeroCount > 0) {
      console.log('\n✅ SUCCESS: Found 0 values in the exported file!');
    } else {
      console.log('\n❌ ISSUE: No 0 values found. The export might not be including all cells.');
    }
    
    console.log('\n🎉 Check completed!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkExportedZeros().catch(console.error);
