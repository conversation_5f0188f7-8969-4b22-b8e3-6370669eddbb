module.exports = {
  "apps": [
    {
      "name": "rpro-import",
      "script": "C:\\Users\\<USER>\\supabase_project\\batchImport.js",
      "args": "\"R:\\Rpro\\BRIDGE\\invdb.dbf\" \"imported_table_rpro\" true",
      "cron_restart": "15 6 * * *",
      "autorestart": false
    },
    {
      "name": "discraft-scheduler",
      "script": "C:\\Users\\<USER>\\supabase_project\\discraftScheduler.js",
      "instances": 1,
      "autorestart": true,
      "watch": false,
      "max_memory_restart": "500M",
      "env": {
        "NODE_ENV": "production"
      },
      "cron_restart": "0 0 * * *",
      "log_date_format": "YYYY-MM-DD HH:mm:ss Z"
    }
  ]
}