import cron from 'node-cron';
import runDailyAutomation from './discraftDailyAutomation.js';

console.log('🕐 Discraft Daily Scheduler Starting...');
console.log('📅 Scheduled to run every day at 11:00 AM CST');
console.log('📧 Reports will be <NAME_EMAIL>');

// Schedule for 11:00 AM CST (17:00 UTC during standard time, 16:00 UTC during daylight time)
// Using 11:00 AM America/Chicago timezone
const cronExpression = '0 11 * * *'; // 11:00 AM every day

// Validate cron expression
if (cron.validate(cronExpression)) {
    console.log('✅ Cron expression is valid');
} else {
    console.error('❌ Invalid cron expression');
    process.exit(1);
}

// Schedule the task
const task = cron.schedule(cronExpression, async () => {
    console.log('\n🔔 Scheduled task triggered!');
    console.log(`⏰ Current time: ${new Date().toLocaleString('en-US', { timeZone: 'America/Chicago' })} CST`);
    
    try {
        await runDailyAutomation();
    } catch (error) {
        console.error('❌ Scheduled task failed:', error);
    }
}, {
    scheduled: true,
    timezone: 'America/Chicago'
});

// Log next execution time
const nextExecution = task.nextDate();
if (nextExecution) {
    console.log(`⏭️  Next execution: ${nextExecution.toLocaleString('en-US', { timeZone: 'America/Chicago' })} CST`);
}

// Keep the process running
console.log('🔄 Scheduler is running... Press Ctrl+C to stop');

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down scheduler...');
    task.destroy();
    console.log('✅ Scheduler stopped');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Received SIGTERM, shutting down...');
    task.destroy();
    process.exit(0);
});

// Optional: Add a test run command
if (process.argv.includes('--test')) {
    console.log('\n🧪 Running test execution...');
    runDailyAutomation().catch(console.error);
}
